import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Hr,
  Button,
} from '@react-email/components';
import { format } from 'date-fns';
import * as React from 'react';

// Define the type for missing document data
export interface MissingDocumentReminderEmailData {
  recipientName: string;
  applicationNumber: string;
  serviceName: string;
  daysSinceLastUpdate: number;
  missingDocuments: {
    fileName: string;
    required: boolean;
    status: string;
    requestReason?: string;
  }[];
  websiteUrl?: string;
  supportEmail?: string;
}

// Status badge color helper
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return '#F59E0B'; // amber
    case 'rejected':
      return '#EF4444'; // red
    case 'approved':
      return '#10B981'; // green
    default:
      return '#6B7280'; // gray
  }
};

export default function MissingDocumentReminderEmail({
  recipientName = 'User',
  applicationNumber = 'APP-2024-001',
  serviceName = 'Immigration Service',
  daysSinceLastUpdate = 7,
  missingDocuments = [
    {
      fileName: 'Passport Copy',
      required: true,
      status: 'pending',
      requestReason: 'Required for identity verification',
    },
  ],
  websiteUrl = process.env.WEBSITE || 'http://localhost:3001',
  supportEmail = process.env.EMAIL || '<EMAIL>',
}: MissingDocumentReminderEmailData) {
  const requiredDocuments = missingDocuments.filter(doc => doc.required);
  const optionalDocuments = missingDocuments.filter(doc => !doc.required);

  return (
    <Html>
      <Head />
      <Preview>
        Document Reminder: {missingDocuments.length} pending documents for {applicationNumber}
      </Preview>
      <Body style={styles.body}>
        <Container style={styles.container}>
          <Section style={styles.main}>
            <Heading style={styles.heading}>
              Document Submission Reminder
            </Heading>
            
            <Text style={styles.paragraph}>
              Dear {recipientName},
            </Text>

            <Text style={styles.paragraph}>
              This is a friendly reminder that your application <strong>{applicationNumber}</strong> for 
              <strong> {serviceName}</strong> has pending documents that require your attention.
            </Text>

            <Text style={styles.paragraph}>
              It has been <strong>{daysSinceLastUpdate} days</strong> since your last update. 
              To avoid delays in processing your application, please submit the following documents:
            </Text>

            <Hr style={styles.divider} />

            {requiredDocuments.length > 0 && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Required Documents ({requiredDocuments.length})
                </Heading>
                {requiredDocuments.map((doc, index) => (
                  <Row key={index} style={styles.documentRow}>
                    <Text style={styles.documentItem}>
                      <span style={{
                        ...styles.statusBadge,
                        backgroundColor: getStatusColor(doc.status),
                      }}>
                        {doc.status.toUpperCase()}
                      </span>
                      <strong>{doc.fileName}</strong>
                      {doc.requestReason && (
                        <Text style={styles.documentReason}>
                          {doc.requestReason}
                        </Text>
                      )}
                    </Text>
                  </Row>
                ))}
              </Section>
            )}

            {optionalDocuments.length > 0 && (
              <Section style={styles.card}>
                <Heading as="h2" style={styles.subheading}>
                  Optional Documents ({optionalDocuments.length})
                </Heading>
                {optionalDocuments.map((doc, index) => (
                  <Row key={index} style={styles.documentRow}>
                    <Text style={styles.documentItem}>
                      <span style={{
                        ...styles.statusBadge,
                        backgroundColor: getStatusColor(doc.status),
                      }}>
                        {doc.status.toUpperCase()}
                      </span>
                      <strong>{doc.fileName}</strong>
                      {doc.requestReason && (
                        <Text style={styles.documentReason}>
                          {doc.requestReason}
                        </Text>
                      )}
                    </Text>
                  </Row>
                ))}
              </Section>
            )}

            <Hr style={styles.divider} />

            <Section style={styles.actionSection}>
              <Text style={styles.paragraph}>
                <strong>Next Steps:</strong>
              </Text>
              <Text style={styles.bulletPoint}>
                • Log in to your account to upload the required documents
              </Text>
              <Text style={styles.bulletPoint}>
                • Ensure all documents are clear, legible, and in accepted formats (PDF, JPG, PNG)
              </Text>
              <Text style={styles.bulletPoint}>
                • Contact our support team if you have any questions or need assistance
              </Text>

              <Button
                href={`${websiteUrl}/login`}
                style={styles.button}
              >
                Upload Documents Now
              </Button>
            </Section>

            <Hr style={styles.divider} />

            <Section style={styles.supportSection}>
              <Text style={styles.supportText}>
                Need help? Contact our support team at{' '}
                <a href={`mailto:${supportEmail}`} style={styles.link}>
                  {supportEmail}
                </a>
              </Text>
            </Section>
          </Section>

          <Section style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} Career Ireland. All rights reserved.
            </Text>
            <Text style={styles.footerText}>
              This is an automated reminder. Please do not reply to this email.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles following the purchase-notification pattern
const styles = {
  body: {
    backgroundColor: '#f6f9fc',
    fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
  },
  container: {
    backgroundColor: '#ffffff',
    margin: '0 auto',
    padding: '20px 0 48px',
    marginBottom: '64px',
  },
  main: {
    backgroundColor: '#ffffff',
    margin: '0 auto',
    padding: '40px 20px',
    borderRadius: '8px',
  },
  heading: {
    fontSize: '32px',
    lineHeight: '1.3',
    fontWeight: '700',
    color: '#1f2937',
    textAlign: 'center' as const,
    marginBottom: '32px',
  },
  subheading: {
    fontSize: '20px',
    lineHeight: '1.4',
    fontWeight: '600',
    color: '#374151',
    marginBottom: '16px',
  },
  paragraph: {
    fontSize: '16px',
    lineHeight: '1.6',
    color: '#374151',
    marginBottom: '16px',
  },
  card: {
    backgroundColor: '#f9fafb',
    border: '1px solid #e5e7eb',
    borderRadius: '8px',
    padding: '24px',
    marginBottom: '24px',
  },
  documentRow: {
    marginBottom: '12px',
  },
  documentItem: {
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#374151',
    marginBottom: '8px',
  },
  documentReason: {
    fontSize: '12px',
    color: '#6b7280',
    fontStyle: 'italic' as const,
    marginTop: '4px',
    marginLeft: '0px',
  },
  statusBadge: {
    display: 'inline-block',
    padding: '2px 8px',
    borderRadius: '12px',
    fontSize: '10px',
    fontWeight: '600',
    color: '#ffffff',
    marginRight: '8px',
    textTransform: 'uppercase' as const,
  },
  actionSection: {
    textAlign: 'center' as const,
    marginTop: '32px',
  },
  bulletPoint: {
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#374151',
    marginBottom: '8px',
    textAlign: 'left' as const,
  },
  button: {
    backgroundColor: '#3b82f6',
    borderRadius: '6px',
    color: '#ffffff',
    fontSize: '16px',
    fontWeight: '600',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
    padding: '12px 24px',
    marginTop: '24px',
  },
  supportSection: {
    textAlign: 'center' as const,
    marginTop: '24px',
  },
  supportText: {
    fontSize: '14px',
    color: '#6b7280',
  },
  divider: {
    borderColor: '#e5e7eb',
    margin: '32px 0',
  },
  link: {
    color: '#3b82f6',
    textDecoration: 'underline',
  },
  footer: {
    textAlign: 'center' as const,
    marginTop: '32px',
    paddingTop: '32px',
    borderTop: '1px solid #e5e7eb',
  },
  footerText: {
    fontSize: '12px',
    color: '#6b7280',
    marginBottom: '8px',
  },
};
