/**
 * Missing Document Email Integration Tests
 * 
 * Unit tests for email template integration and sending functionality.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { Test, TestingModule } from '@nestjs/testing';
import { render } from '@react-email/components';
import { EmailTemplateIntegrationService } from '../../src/application/services/email-template-integration.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import MissingDocumentReminderEmail from '../../src/template/missing-document-reminder';

// Mock the render function
jest.mock('@react-email/components', () => ({
  render: jest.fn(),
}));

// Mock the email template
jest.mock('../../src/template/missing-document-reminder', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('EmailTemplateIntegrationService - Missing Document Reminders', () => {
  let service: EmailTemplateIntegrationService;
  let mailerService: jest.Mocked<MailerService>;
  let loggerService: jest.Mocked<LoggerService>;

  const mockMailerService = {
    sendEmail: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailTemplateIntegrationService,
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<EmailTemplateIntegrationService>(EmailTemplateIntegrationService);
    mailerService = module.get(MailerService);
    loggerService = module.get(LoggerService);

    // Setup environment variables
    process.env.EMAIL = '<EMAIL>';
    process.env.WEBSITE = 'https://test.careerireland.com';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendMissingDocumentReminderEmail', () => {
    const mockEmailData = {
      recipientName: 'John Doe',
      applicationNumber: 'APP-2024-001',
      serviceName: 'Immigration Service',
      daysSinceLastUpdate: 7,
      missingDocuments: [
        {
          fileName: 'Passport Copy',
          required: true,
          status: 'pending',
          requestReason: 'Required for identity verification',
        },
        {
          fileName: 'Bank Statement',
          required: false,
          status: 'rejected',
          requestReason: 'Document quality insufficient',
        },
      ],
      websiteUrl: 'https://test.careerireland.com',
      supportEmail: '<EMAIL>',
    };

    it('should send missing document reminder email successfully', async () => {
      // Arrange
      const mockHtml = '<html><body>Test email content</body></html>';
      (render as jest.Mock).mockResolvedValue(mockHtml);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email-123' });

      // Act
      await service.sendMissingDocumentReminderEmail('<EMAIL>', mockEmailData);

      // Assert
      expect(render).toHaveBeenCalledWith(
        expect.objectContaining({
          recipientName: 'John Doe',
          applicationNumber: 'APP-2024-001',
          serviceName: 'Immigration Service',
          daysSinceLastUpdate: 7,
          missingDocuments: mockEmailData.missingDocuments,
          websiteUrl: 'https://test.careerireland.com',
          supportEmail: '<EMAIL>',
        }),
      );

      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Document Reminder - APP-2024-001',
        html: mockHtml,
        cc: [],
      });

      expect(loggerService.log).toHaveBeenCalledWith(
        'Missing document reminder email sent successfully to: <EMAIL>',
      );
    });

    it('should use default values for missing environment variables', async () => {
      // Arrange
      delete process.env.EMAIL;
      delete process.env.WEBSITE;
      
      const emailDataWithoutDefaults = {
        ...mockEmailData,
        websiteUrl: undefined,
        supportEmail: undefined,
      };

      const mockHtml = '<html><body>Test email content</body></html>';
      (render as jest.Mock).mockResolvedValue(mockHtml);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email-123' });

      // Act
      await service.sendMissingDocumentReminderEmail('<EMAIL>', emailDataWithoutDefaults);

      // Assert
      expect(render).toHaveBeenCalledWith(
        expect.objectContaining({
          websiteUrl: 'http://localhost:3001',
          supportEmail: '<EMAIL>',
        }),
      );

      expect(mockMailerService.sendEmail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Document Reminder - APP-2024-001',
        html: mockHtml,
        cc: [],
      });
    });

    it('should handle template rendering errors and use fallback', async () => {
      // Arrange
      const renderError = new Error('Template rendering failed');
      (render as jest.Mock).mockRejectedValue(renderError);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email-123' });

      // Act
      await service.sendMissingDocumentReminderEmail('<EMAIL>', mockEmailData);

      // Assert
      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send missing document reminder <NAME_EMAIL>: Template rendering failed',
        expect.any(String),
        'EmailTemplateIntegrationService',
      );

      // Should call sendEmail twice - once for main template (fails), once for fallback
      expect(mockMailerService.sendEmail).toHaveBeenCalledTimes(2);
      
      // Check fallback email was sent
      const fallbackCall = mockMailerService.sendEmail.mock.calls[1][0];
      expect(fallbackCall.subject).toBe('Document Reminder - APP-2024-001');
      expect(fallbackCall.html).toContain('Document Submission Reminder');
      expect(fallbackCall.html).toContain('John Doe');
      expect(fallbackCall.html).toContain('APP-2024-001');
    });

    it('should handle mailer service errors in fallback', async () => {
      // Arrange
      const renderError = new Error('Template rendering failed');
      const mailerError = new Error('Email service unavailable');
      
      (render as jest.Mock).mockRejectedValue(renderError);
      mockMailerService.sendEmail
        .mockRejectedValueOnce(mailerError) // First call fails
        .mockRejectedValueOnce(mailerError); // Fallback call also fails

      // Act & Assert
      await expect(
        service.sendMissingDocumentReminderEmail('<EMAIL>', mockEmailData)
      ).rejects.toThrow('Email service unavailable');

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send fallback missing document reminder email: Email service unavailable',
        expect.any(String),
        'EmailTemplateIntegrationService',
      );
    });

    it('should generate correct fallback template content', async () => {
      // Arrange
      const renderError = new Error('Template rendering failed');
      (render as jest.Mock).mockRejectedValue(renderError);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email-123' });

      // Act
      await service.sendMissingDocumentReminderEmail('<EMAIL>', mockEmailData);

      // Assert
      const fallbackCall = mockMailerService.sendEmail.mock.calls[1][0];
      const fallbackHtml = fallbackCall.html;

      // Check required content is present
      expect(fallbackHtml).toContain('John Doe');
      expect(fallbackHtml).toContain('APP-2024-001');
      expect(fallbackHtml).toContain('Immigration Service');
      expect(fallbackHtml).toContain('7 days');
      expect(fallbackHtml).toContain('Required Documents (1)');
      expect(fallbackHtml).toContain('Optional Documents (1)');
      expect(fallbackHtml).toContain('Passport Copy');
      expect(fallbackHtml).toContain('Bank Statement');
      expect(fallbackHtml).toContain('PENDING');
      expect(fallbackHtml).toContain('REJECTED');
      expect(fallbackHtml).toContain('Required for identity verification');
      expect(fallbackHtml).toContain('Document quality insufficient');
      expect(fallbackHtml).toContain('https://test.careerireland.com/login');
      expect(fallbackHtml).toContain('<EMAIL>');
    });

    it('should handle empty document lists in fallback template', async () => {
      // Arrange
      const emailDataWithNoDocuments = {
        ...mockEmailData,
        missingDocuments: [],
      };

      const renderError = new Error('Template rendering failed');
      (render as jest.Mock).mockRejectedValue(renderError);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email-123' });

      // Act
      await service.sendMissingDocumentReminderEmail('<EMAIL>', emailDataWithNoDocuments);

      // Assert
      const fallbackCall = mockMailerService.sendEmail.mock.calls[1][0];
      const fallbackHtml = fallbackCall.html;

      // Should not contain document sections when no documents
      expect(fallbackHtml).not.toContain('Required Documents');
      expect(fallbackHtml).not.toContain('Optional Documents');
    });

    it('should separate required and optional documents correctly in fallback', async () => {
      // Arrange
      const emailDataWithMixedDocuments = {
        ...mockEmailData,
        missingDocuments: [
          { fileName: 'Passport', required: true, status: 'pending' },
          { fileName: 'Visa', required: true, status: 'rejected' },
          { fileName: 'Photo', required: false, status: 'pending' },
          { fileName: 'Cover Letter', required: false, status: 'rejected' },
        ],
      };

      const renderError = new Error('Template rendering failed');
      (render as jest.Mock).mockRejectedValue(renderError);
      mockMailerService.sendEmail.mockResolvedValue({ id: 'email-123' });

      // Act
      await service.sendMissingDocumentReminderEmail('<EMAIL>', emailDataWithMixedDocuments);

      // Assert
      const fallbackCall = mockMailerService.sendEmail.mock.calls[1][0];
      const fallbackHtml = fallbackCall.html;

      expect(fallbackHtml).toContain('Required Documents (2)');
      expect(fallbackHtml).toContain('Optional Documents (2)');
    });
  });
});
