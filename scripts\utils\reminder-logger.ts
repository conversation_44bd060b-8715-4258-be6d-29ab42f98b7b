/**
 * Reminder Logger Utility
 * 
 * Dedicated logging utility for the missing document reminder system
 * with file-based error logging and monitoring capabilities.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import * as fs from 'fs';
import * as path from 'path';

export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  stack?: string;
  metadata?: Record<string, any>;
}

export class ReminderLogger {
  private readonly logDir: string;
  private readonly logFile: string;
  private readonly errorLogFile: string;
  private readonly maxLogSize: number = 10 * 1024 * 1024; // 10MB
  private readonly maxLogFiles: number = 5;

  constructor(logDir?: string) {
    this.logDir = logDir || path.join(process.cwd(), 'logs', 'reminders');
    this.logFile = path.join(this.logDir, 'missing-document-reminders.log');
    this.errorLogFile = path.join(this.logDir, 'missing-document-reminders-errors.log');
    this.ensureLogDirectory();
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context, undefined, metadata);
  }

  /**
   * Log info message
   */
  info(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context, undefined, metadata);
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context, undefined, metadata);
  }

  /**
   * Log error message
   */
  error(message: string, context?: string, stack?: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, stack, metadata);
  }

  /**
   * Log application processing start
   */
  logApplicationProcessingStart(applicationNumber: string, userEmail: string): void {
    this.info(`Processing application ${applicationNumber}`, 'ApplicationProcessor', {
      applicationNumber,
      userEmail,
      action: 'start_processing',
    });
  }

  /**
   * Log application processing success
   */
  logApplicationProcessingSuccess(applicationNumber: string, userEmail: string, remindersSent: number): void {
    this.info(`Successfully processed application ${applicationNumber}`, 'ApplicationProcessor', {
      applicationNumber,
      userEmail,
      remindersSent,
      action: 'processing_success',
    });
  }

  /**
   * Log application processing error
   */
  logApplicationProcessingError(applicationNumber: string, userEmail: string, error: Error): void {
    this.error(`Failed to process application ${applicationNumber}: ${error.message}`, 'ApplicationProcessor', error.stack, {
      applicationNumber,
      userEmail,
      action: 'processing_error',
      errorName: error.name,
    });
  }

  /**
   * Log email sending attempt
   */
  logEmailSendingAttempt(recipientEmail: string, applicationNumber: string, documentCount: number): void {
    this.info(`Attempting to send reminder email`, 'EmailSender', {
      recipientEmail,
      applicationNumber,
      documentCount,
      action: 'email_attempt',
    });
  }

  /**
   * Log email sending success
   */
  logEmailSendingSuccess(recipientEmail: string, applicationNumber: string): void {
    this.info(`Successfully sent reminder email`, 'EmailSender', {
      recipientEmail,
      applicationNumber,
      action: 'email_success',
    });
  }

  /**
   * Log email sending error
   */
  logEmailSendingError(recipientEmail: string, applicationNumber: string, error: Error): void {
    this.error(`Failed to send reminder email: ${error.message}`, 'EmailSender', error.stack, {
      recipientEmail,
      applicationNumber,
      action: 'email_error',
      errorName: error.name,
    });
  }

  /**
   * Log database query performance
   */
  logDatabaseQuery(query: string, executionTime: number, resultCount: number): void {
    this.debug(`Database query executed`, 'DatabaseQuery', {
      query,
      executionTime,
      resultCount,
      action: 'db_query',
    });
  }

  /**
   * Log script execution summary
   */
  logExecutionSummary(summary: {
    totalApplications: number;
    eligibleApplications: number;
    remindersSent: number;
    errors: number;
    executionTime: number;
  }): void {
    this.info('Script execution completed', 'ScriptSummary', {
      ...summary,
      action: 'execution_summary',
    });
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, context?: string, stack?: string, metadata?: Record<string, any>): void {
    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      stack,
      metadata,
    };

    const logLine = this.formatLogEntry(logEntry);

    // Write to main log file
    this.writeToFile(this.logFile, logLine);

    // Write errors to separate error log file
    if (level === LogLevel.ERROR) {
      this.writeToFile(this.errorLogFile, logLine);
    }

    // Also log to console for immediate feedback
    this.logToConsole(logEntry);
  }

  /**
   * Format log entry for file output
   */
  private formatLogEntry(entry: LogEntry): string {
    let logLine = `[${entry.timestamp}] [${entry.level}]`;
    
    if (entry.context) {
      logLine += ` [${entry.context}]`;
    }
    
    logLine += ` ${entry.message}`;
    
    if (entry.metadata) {
      logLine += ` | Metadata: ${JSON.stringify(entry.metadata)}`;
    }
    
    if (entry.stack) {
      logLine += `\nStack Trace:\n${entry.stack}`;
    }
    
    return logLine + '\n';
  }

  /**
   * Log to console with appropriate method
   */
  private logToConsole(entry: LogEntry): void {
    const message = `[${entry.level}] ${entry.context ? `[${entry.context}] ` : ''}${entry.message}`;
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message);
        break;
      case LogLevel.INFO:
        console.log(message);
        break;
      case LogLevel.WARN:
        console.warn(message);
        break;
      case LogLevel.ERROR:
        console.error(message);
        if (entry.stack) {
          console.error(entry.stack);
        }
        break;
    }
  }

  /**
   * Write to log file with rotation
   */
  private writeToFile(filePath: string, content: string): void {
    try {
      // Check if log rotation is needed
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        if (stats.size > this.maxLogSize) {
          this.rotateLogFile(filePath);
        }
      }

      fs.appendFileSync(filePath, content);
    } catch (error) {
      console.error(`Failed to write to log file ${filePath}:`, error);
    }
  }

  /**
   * Rotate log file when it gets too large
   */
  private rotateLogFile(filePath: string): void {
    try {
      const dir = path.dirname(filePath);
      const basename = path.basename(filePath, path.extname(filePath));
      const extension = path.extname(filePath);

      // Rotate existing files
      for (let i = this.maxLogFiles - 1; i > 0; i--) {
        const oldFile = path.join(dir, `${basename}.${i}${extension}`);
        const newFile = path.join(dir, `${basename}.${i + 1}${extension}`);
        
        if (fs.existsSync(oldFile)) {
          if (i === this.maxLogFiles - 1) {
            fs.unlinkSync(oldFile); // Delete oldest file
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Move current file to .1
      const rotatedFile = path.join(dir, `${basename}.1${extension}`);
      fs.renameSync(filePath, rotatedFile);
    } catch (error) {
      console.error(`Failed to rotate log file ${filePath}:`, error);
    }
  }

  /**
   * Ensure log directory exists
   */
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Get log file paths for external access
   */
  getLogFilePaths(): { logFile: string; errorLogFile: string } {
    return {
      logFile: this.logFile,
      errorLogFile: this.errorLogFile,
    };
  }

  /**
   * Clear log files (useful for testing)
   */
  clearLogs(): void {
    try {
      if (fs.existsSync(this.logFile)) {
        fs.unlinkSync(this.logFile);
      }
      if (fs.existsSync(this.errorLogFile)) {
        fs.unlinkSync(this.errorLogFile);
      }
    } catch (error) {
      console.error('Failed to clear log files:', error);
    }
  }
}
