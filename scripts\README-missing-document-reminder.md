# Missing Document Reminder System

A comprehensive system for sending automated email reminders to users with missing or pending documents in their applications.

## Overview

The Missing Document Reminder System consists of:

1. **Database Query Service** - Finds applications with missing documents
2. **Email Template** - React-based email template with fallback support
3. **Email Integration Service** - Handles email sending with error recovery
4. **Main Script** - Orchestrates the entire reminder process
5. **File-based Logging** - Comprehensive logging for debugging and monitoring
6. **Unit Tests** - Complete test coverage for all components

## Features

- ✅ Queries applications with pending/rejected/missing documents
- ✅ Calculates days since last update
- ✅ Respects user notification settings for reminder timing
- ✅ Excludes applications older than 30 days
- ✅ Sends professional HTML emails with document lists
- ✅ Fallback email templates for error recovery
- ✅ Non-blocking email sending
- ✅ Comprehensive file-based logging
- ✅ Error handling and recovery
- ✅ Can be run as scheduled job or manually

## Usage

### Manual Execution

```bash
# Run the script directly
npm run script:missing-document-reminder

# Or using ts-node
ts-node scripts/missing-document-reminder.ts
```

### Scheduled Execution

Add to your cron jobs or task scheduler:

```bash
# Run daily at 9 AM
0 9 * * * cd /path/to/project && npm run script:missing-document-reminder

# Run twice daily (9 AM and 2 PM)
0 9,14 * * * cd /path/to/project && npm run script:missing-document-reminder
```

### Environment Variables

Ensure these environment variables are set:

```bash
EMAIL=<EMAIL>
EMAIL_API_KEY=your-resend-api-key
WEBSITE=https://your-website.com
```

## Configuration

### Notification Settings

Users can configure their reminder frequency in `config/notification-settings.json`:

```json
{
  "user_id": "user-123",
  "missing_document_reminder_days": 7,
  "agent_assigned": true,
  "case_status_update": true,
  "document_rejection": true
}
```

### Default Settings

- **Reminder Days**: 7 days (configurable per user)
- **Maximum Age**: 30 days (applications older than this are excluded)
- **Default Service Name**: Uses service type if display name not found

## Database Query Logic

The system queries applications where:

1. **Status** is NOT "Completed"
2. **Documents** have one of:
   - `status = 'pending'`
   - `status = 'rejected'`
   - `file_url = ''` (empty file URL indicates missing document)

3. **Timing** matches:
   - Days since last update equals user's reminder setting
   - Application is not older than 30 days

## Email Template

The email includes:

- **Personalized greeting** with recipient name
- **Application details** (number, service type, days since update)
- **Required documents** list with status badges
- **Optional documents** list with status badges
- **Next steps** and instructions
- **Call-to-action** button to upload documents
- **Support contact** information
- **Professional branding** consistent with other templates

### Fallback Template

If the React template fails to render, a fallback HTML template is used with:

- Same content structure
- Simplified styling
- All essential information preserved
- Error logging for debugging

## Logging

### Log Files

- **Main Log**: `logs/reminders/missing-document-reminders.log`
- **Error Log**: `logs/reminders/missing-document-reminders-errors.log`

### Log Levels

- **DEBUG**: Database queries, performance metrics
- **INFO**: Process steps, successful operations
- **WARN**: Non-critical issues, fallback usage
- **ERROR**: Failures, exceptions, critical issues

### Log Rotation

- **Max Size**: 10MB per file
- **Max Files**: 5 rotated files kept
- **Automatic**: Rotation happens when size limit reached

## Testing

### Run All Tests

```bash
# Run missing document reminder tests
npm test test/scripts/missing-document-reminder.service.spec.ts
npm test test/scripts/missing-document-email-integration.spec.ts
npm test test/scripts/missing-document-reminder-script.spec.ts
npm test test/scripts/reminder-logger.spec.ts

# Run with coverage
npm run test:cov -- test/scripts/
```

### Test Coverage

- ✅ Database query logic and error handling
- ✅ Date calculations and timing logic
- ✅ Email template rendering and fallback
- ✅ Email sending success and failure scenarios
- ✅ Script execution and error recovery
- ✅ File-based logging functionality
- ✅ User notification settings integration

## Monitoring

### Success Metrics

- Applications processed
- Reminders sent successfully
- Execution time
- Error rate

### Error Monitoring

- Database connection failures
- Email service outages
- Template rendering errors
- File system issues

### Log Analysis

```bash
# Check recent errors
tail -f logs/reminders/missing-document-reminders-errors.log

# Count successful reminders today
grep "$(date +%Y-%m-%d)" logs/reminders/missing-document-reminders.log | grep "Successfully sent reminder email" | wc -l

# Find applications with repeated failures
grep "Failed to send reminder" logs/reminders/missing-document-reminders-errors.log | sort | uniq -c
```

## Troubleshooting

### Common Issues

1. **No emails sent**
   - Check notification settings configuration
   - Verify applications have missing documents
   - Confirm timing matches reminder days

2. **Email delivery failures**
   - Verify EMAIL_API_KEY is valid
   - Check Resend service status
   - Review error logs for specific failures

3. **Database query errors**
   - Check database connection
   - Verify Prisma schema is up to date
   - Review application and document table structure

4. **Template rendering errors**
   - Check React email dependencies
   - Verify template imports
   - Fallback template should still work

### Debug Mode

Set environment variable for verbose logging:

```bash
DEBUG=true npm run script:missing-document-reminder
```

## Architecture

```
scripts/
├── missing-document-reminder.ts          # Main script
├── missing-document-reminder.service.ts  # Database queries
└── utils/
    └── reminder-logger.ts                 # File-based logging

src/
├── template/
│   └── missing-document-reminder.tsx     # Email template
└── application/services/
    └── email-template-integration.service.ts  # Email sending

test/scripts/
├── missing-document-reminder.service.spec.ts
├── missing-document-email-integration.spec.ts
├── missing-document-reminder-script.spec.ts
└── reminder-logger.spec.ts
```

## Contributing

When modifying the system:

1. Update unit tests for any logic changes
2. Test email templates in multiple email clients
3. Verify logging captures all important events
4. Update this documentation for new features
5. Follow existing error handling patterns

## Security Considerations

- Email addresses are logged but not stored permanently
- User data is handled according to GDPR requirements
- Error messages don't expose sensitive information
- File-based logs should be secured and rotated regularly
