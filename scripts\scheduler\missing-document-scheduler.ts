/**
 * Missing Document Reminder Scheduler
 * 
 * Self-contained scheduler that runs the missing document reminder process
 * at specified intervals without requiring external cron setup.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';
import { EmailTemplateIntegrationService } from '../../src/application/services/email-template-integration.service';
import { MissingDocumentReminderService } from '../missing-document-reminder.service';
import { ReminderLogger } from '../utils/reminder-logger';
import { SchedulerConfig, loadSchedulerConfig, validateSchedulerConfig, getNextScheduledTime, isScheduledTime, formatConfigForLogging } from '../config/scheduler-config';

// NestJS module for dependency injection
@Module({
  providers: [
    PrismaService,
    LoggerService,
    MailerService,
    NotificationSettingsStorageService,
    EmailTemplateIntegrationService,
    MissingDocumentReminderService,
  ],
})
class SchedulerModule {}

export interface SchedulerStatus {
  running: boolean;
  lastExecution?: Date;
  nextExecution?: Date;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  currentRetryCount: number;
  uptime: number; // milliseconds
}

/**
 * Self-contained scheduler for missing document reminders
 */
export class MissingDocumentScheduler {
  private readonly logger = new Logger(MissingDocumentScheduler.name);
  private readonly reminderLogger = new ReminderLogger();
  private readonly config: SchedulerConfig;
  
  private app: any;
  private reminderService: MissingDocumentReminderService;
  private emailService: EmailTemplateIntegrationService;
  private loggerService: LoggerService;
  
  private isRunning = false;
  private shouldStop = false;
  private checkInterval: NodeJS.Timeout | null = null;
  private retryTimeout: NodeJS.Timeout | null = null;
  private startTime: Date;
  
  private status: SchedulerStatus = {
    running: false,
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    currentRetryCount: 0,
    uptime: 0,
  };

  constructor() {
    this.config = loadSchedulerConfig();
    this.startTime = new Date();
    
    // Validate configuration
    const configErrors = validateSchedulerConfig(this.config);
    if (configErrors.length > 0) {
      throw new Error(`Invalid scheduler configuration: ${configErrors.join(', ')}`);
    }
    
    // Setup graceful shutdown handlers
    this.setupGracefulShutdown();
  }

  /**
   * Initialize the scheduler and its dependencies
   */
  async initialize(): Promise<void> {
    try {
      this.logger.log('Initializing Missing Document Scheduler...');
      this.reminderLogger.info('Scheduler initialization started', 'Scheduler');
      
      // Create NestJS application context
      this.app = await NestFactory.createApplicationContext(SchedulerModule, {
        logger: this.config.logging.level === 'debug' ? ['error', 'warn', 'log', 'debug'] : ['error', 'warn', 'log'],
      });

      // Get services from DI container
      this.reminderService = this.app.get(MissingDocumentReminderService);
      this.emailService = this.app.get(EmailTemplateIntegrationService);
      this.loggerService = this.app.get(LoggerService);

      this.logger.log('Scheduler initialized successfully');
      this.reminderLogger.info('Scheduler initialized successfully', 'Scheduler', {
        config: formatConfigForLogging(this.config),
      });

    } catch (error) {
      this.logger.error(`Failed to initialize scheduler: ${error.message}`, error.stack);
      this.reminderLogger.error('Scheduler initialization failed', 'Scheduler', error.stack, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Start the scheduler
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Scheduler is already running');
      return;
    }

    if (!this.config.enabled) {
      this.logger.log('Scheduler is disabled in configuration');
      this.reminderLogger.info('Scheduler is disabled in configuration', 'Scheduler');
      return;
    }

    try {
      await this.initialize();
      
      this.isRunning = true;
      this.shouldStop = false;
      this.status.running = true;
      this.status.nextExecution = getNextScheduledTime(this.config);
      
      this.logger.log(`Scheduler started. Next execution: ${this.status.nextExecution?.toISOString()}`);
      this.reminderLogger.info('Scheduler started', 'Scheduler', {
        nextExecution: this.status.nextExecution?.toISOString(),
        schedule: `${this.config.schedule.hour}:${this.config.schedule.minute.toString().padStart(2, '0')}`,
      });

      // Start the main scheduling loop
      this.scheduleNextCheck();

    } catch (error) {
      this.logger.error(`Failed to start scheduler: ${error.message}`, error.stack);
      this.reminderLogger.error('Failed to start scheduler', 'Scheduler', error.stack, {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Stop the scheduler gracefully
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Scheduler is not running');
      return;
    }

    this.logger.log('Stopping scheduler...');
    this.reminderLogger.info('Scheduler stop requested', 'Scheduler');
    
    this.shouldStop = true;
    this.isRunning = false;
    this.status.running = false;

    // Clear intervals and timeouts
    if (this.checkInterval) {
      clearTimeout(this.checkInterval);
      this.checkInterval = null;
    }
    
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }

    // Close NestJS application context
    if (this.app) {
      await this.app.close();
    }

    this.logger.log('Scheduler stopped successfully');
    this.reminderLogger.info('Scheduler stopped successfully', 'Scheduler', {
      uptime: Date.now() - this.startTime.getTime(),
      totalExecutions: this.status.totalExecutions,
      successfulExecutions: this.status.successfulExecutions,
      failedExecutions: this.status.failedExecutions,
    });
  }

  /**
   * Get current scheduler status
   */
  getStatus(): SchedulerStatus {
    return {
      ...this.status,
      uptime: Date.now() - this.startTime.getTime(),
    };
  }

  /**
   * Schedule the next check
   */
  private scheduleNextCheck(): void {
    if (this.shouldStop) return;

    this.checkInterval = setTimeout(() => {
      this.checkSchedule();
    }, this.config.intervals.checkInterval);
  }

  /**
   * Check if it's time to execute the reminder process
   */
  private async checkSchedule(): Promise<void> {
    if (this.shouldStop) return;

    try {
      if (isScheduledTime(this.config)) {
        this.logger.log('Scheduled time reached, executing reminder process...');
        await this.executeReminderProcess();
      }
    } catch (error) {
      this.logger.error(`Error during schedule check: ${error.message}`, error.stack);
      this.reminderLogger.error('Error during schedule check', 'Scheduler', error.stack, {
        error: error.message,
      });
    }

    // Schedule next check
    this.scheduleNextCheck();
  }

  /**
   * Execute the missing document reminder process
   */
  private async executeReminderProcess(): Promise<void> {
    const executionStart = Date.now();
    this.status.totalExecutions++;
    this.status.lastExecution = new Date();
    this.status.nextExecution = getNextScheduledTime(this.config);

    try {
      this.reminderLogger.info('Starting scheduled reminder execution', 'Scheduler', {
        executionNumber: this.status.totalExecutions,
        scheduledTime: this.status.lastExecution.toISOString(),
      });

      // Find applications with missing documents
      const applications = await this.reminderService.findApplicationsWithMissingDocuments();
      
      let remindersSent = 0;
      const errors: string[] = [];

      // Send reminders for each application
      for (const application of applications) {
        try {
          await this.sendReminderEmail(application);
          remindersSent++;
        } catch (error) {
          const errorMsg = `Failed to send reminder for ${application.application_number}: ${error.message}`;
          errors.push(errorMsg);
          this.reminderLogger.error(errorMsg, 'Scheduler', error.stack);
        }
      }

      const executionTime = Date.now() - executionStart;
      this.status.successfulExecutions++;
      this.status.currentRetryCount = 0; // Reset retry count on success

      this.logger.log(`Reminder execution completed: ${remindersSent}/${applications.length} sent in ${executionTime}ms`);
      this.reminderLogger.info('Scheduled reminder execution completed', 'Scheduler', {
        applicationsProcessed: applications.length,
        remindersSent,
        errors: errors.length,
        executionTime,
        nextExecution: this.status.nextExecution?.toISOString(),
      });

    } catch (error) {
      this.status.failedExecutions++;
      this.status.currentRetryCount++;

      this.logger.error(`Reminder execution failed: ${error.message}`, error.stack);
      this.reminderLogger.error('Scheduled reminder execution failed', 'Scheduler', error.stack, {
        error: error.message,
        retryCount: this.status.currentRetryCount,
        maxRetries: this.config.intervals.maxRetries,
      });

      // Schedule retry if within retry limit
      if (this.status.currentRetryCount < this.config.intervals.maxRetries) {
        this.scheduleRetry();
      } else {
        this.logger.error('Maximum retry attempts reached, skipping until next scheduled time');
        this.reminderLogger.error('Maximum retry attempts reached', 'Scheduler', undefined, {
          retryCount: this.status.currentRetryCount,
          maxRetries: this.config.intervals.maxRetries,
        });
        this.status.currentRetryCount = 0; // Reset for next scheduled time
      }
    }
  }

  /**
   * Send reminder email for a specific application
   */
  private async sendReminderEmail(application: any): Promise<void> {
    const recipientEmail = this.reminderService.getRecipientEmail(application);
    const recipientName = this.reminderService.getRecipientName(application);

    const missingDocuments = application.missingDocuments.map((doc: any) => ({
      fileName: doc.file_name,
      required: doc.required,
      status: doc.status,
      requestReason: doc.request_reason,
    }));

    const emailData = {
      recipientName,
      applicationNumber: application.application_number,
      serviceName: this.getServiceDisplayName(application.service_type),
      daysSinceLastUpdate: application.daysSinceLastUpdate,
      missingDocuments,
    };

    await this.emailService.sendMissingDocumentReminderEmail(recipientEmail, emailData);
  }

  /**
   * Get display name for service type
   */
  private getServiceDisplayName(serviceType: string): string {
    const serviceNames: Record<string, string> = {
      immigration: 'Immigration Service',
      visa: 'Visa Application Service',
      consultation: 'Consultation Service',
      training: 'Training Service',
      package: 'Service Package',
    };

    return serviceNames[serviceType] || serviceType;
  }

  /**
   * Schedule a retry execution
   */
  private scheduleRetry(): void {
    if (this.shouldStop) return;

    this.logger.log(`Scheduling retry in ${this.config.intervals.retryInterval}ms (attempt ${this.status.currentRetryCount}/${this.config.intervals.maxRetries})`);
    
    this.retryTimeout = setTimeout(async () => {
      await this.executeReminderProcess();
    }, this.config.intervals.retryInterval);
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      this.logger.log(`Received ${signal}, initiating graceful shutdown...`);
      this.reminderLogger.info(`Graceful shutdown initiated`, 'Scheduler', { signal });
      
      try {
        await Promise.race([
          this.stop(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Shutdown timeout')), this.config.gracefulShutdown.timeout)
          ),
        ]);
        process.exit(0);
      } catch (error) {
        this.logger.error(`Graceful shutdown failed: ${error.message}`, error.stack);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // For nodemon
  }
}
