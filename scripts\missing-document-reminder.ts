#!/usr/bin/env ts-node

/**
 * Missing Document Remind<PERSON>
 *
 * This script implements a missing document reminder system that:
 * 1. Queries applications with missing documents
 * 2. Calculates days since last update
 * 3. Sends email reminders based on notification settings
 * 4. Logs all activities for debugging
 * 5. Supports both one-time execution and self-contained scheduling
 *
 * Usage:
 *   One-time execution:
 *     npm run script:missing-document-reminder
 *     ts-node scripts/missing-document-reminder.ts
 *
 *   Scheduler mode (long-running process):
 *     npm run script:missing-document-reminder -- --scheduler
 *     ts-node scripts/missing-document-reminder.ts --scheduler
 *
 *   Check scheduler status:
 *     npm run script:missing-document-reminder -- --status
 *
 * Environment Variables:
 *   SCHEDULER_ENABLED=true/false
 *   SCHEDULER_HOUR=9 (0-23)
 *   SCHEDULER_MINUTE=0 (0-59)
 *   SCHEDULER_TIMEZONE=UTC
 *   SCHEDULER_CHECK_INTERVAL=60000 (milliseconds)
 *   SCHEDULER_LOG_LEVEL=info/debug/warn/error
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { PrismaService } from '../src/utils/prisma.service';
import { LoggerService } from '../src/utils/logger.service';
import { MailerService } from '../src/mailer/mailer.service';
import { NotificationSettingsStorageService } from '../src/utils/notification-settings-storage.service';
import { EmailTemplateIntegrationService } from '../src/application/services/email-template-integration.service';
import {
  MissingDocumentReminderService,
  MissingDocumentApplication,
} from './missing-document-reminder.service';
import { ReminderLogger } from './utils/reminder-logger';
import { MissingDocumentScheduler } from './scheduler/missing-document-scheduler';

// Create a minimal NestJS module for dependency injection
import { Module } from '@nestjs/common';

@Module({
  providers: [
    PrismaService,
    LoggerService,
    MailerService,
    NotificationSettingsStorageService,
    EmailTemplateIntegrationService,
    MissingDocumentReminderService,
  ],
})
class MissingDocumentReminderModule {}

interface ScriptResult {
  success: boolean;
  totalApplicationsChecked: number;
  remindersEligible: number;
  remindersSent: number;
  errors: string[];
  executionTime: number;
}

class MissingDocumentReminderScript {
  private readonly logger = new Logger(MissingDocumentReminderScript.name);
  private readonly reminderLogger = new ReminderLogger();

  constructor(
    private readonly reminderService: MissingDocumentReminderService,
    private readonly emailService: EmailTemplateIntegrationService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Main execution method
   */
  async execute(): Promise<ScriptResult> {
    const startTime = Date.now();
    const result: ScriptResult = {
      success: false,
      totalApplicationsChecked: 0,
      remindersEligible: 0,
      remindersSent: 0,
      errors: [],
      executionTime: 0,
    };

    try {
      this.logger.log('Starting missing document reminder process...');
      this.reminderLogger.info(
        'Missing document reminder process started',
        'ScriptExecution',
      );

      // Find applications with missing documents
      const queryStart = Date.now();
      const applications =
        await this.reminderService.findApplicationsWithMissingDocuments();
      const queryTime = Date.now() - queryStart;

      result.totalApplicationsChecked = applications.length;
      result.remindersEligible = applications.length;

      this.logger.log(
        `Found ${applications.length} applications eligible for reminders`,
      );
      this.reminderLogger.info(
        `Found ${applications.length} applications eligible for reminders`,
        'ScriptExecution',
      );
      this.reminderLogger.logDatabaseQuery(
        'findApplicationsWithMissingDocuments',
        queryTime,
        applications.length,
      );

      // Send reminders for each application
      for (const application of applications) {
        try {
          await this.sendReminderEmail(application);
          result.remindersSent++;

          this.logger.log(
            `Reminder sent for application: ${application.application_number}`,
          );
          this.reminderLogger.info(
            `Reminder sent for application: ${application.application_number}`,
            'ScriptExecution',
          );
        } catch (error) {
          const errorMsg = `Failed to send reminder for ${application.application_number}: ${error.message}`;
          result.errors.push(errorMsg);

          this.logger.error(errorMsg);
          this.reminderLogger.error(errorMsg, 'ScriptExecution', error.stack);
        }
      }

      result.success = result.errors.length === 0;
      result.executionTime = Date.now() - startTime;

      // Log summary
      const summary = `Process completed. Sent ${result.remindersSent}/${result.remindersEligible} reminders in ${result.executionTime}ms`;
      this.logger.log(summary);
      this.reminderLogger.info(summary, 'ScriptExecution');

      if (result.errors.length > 0) {
        this.logger.warn(
          `${result.errors.length} errors occurred during execution`,
        );
        this.reminderLogger.warn(
          `${result.errors.length} errors occurred during execution`,
          'ScriptExecution',
        );
      }

      return result;
    } catch (error) {
      result.success = false;
      result.executionTime = Date.now() - startTime;
      result.errors.push(`Script execution failed: ${error.message}`);

      this.logger.error(
        `Script execution failed: ${error.message}`,
        error.stack,
      );
      this.reminderLogger.error(
        `Script execution failed: ${error.message}`,
        'ScriptExecution',
        error.stack,
      );

      throw error;
    }
  }

  /**
   * Send reminder email for a specific application
   */
  private async sendReminderEmail(
    application: MissingDocumentApplication,
  ): Promise<void> {
    try {
      const recipientEmail =
        this.reminderService.getRecipientEmail(application);
      const recipientName = this.reminderService.getRecipientName(application);

      // Transform missing documents for email template
      const missingDocuments = application.missingDocuments.map((doc) => ({
        fileName: doc.file_name,
        required: doc.required,
        status: doc.status,
        requestReason: doc.request_reason,
      }));

      // Prepare email data
      const emailData = {
        recipientName,
        applicationNumber: application.application_number,
        serviceName: this.getServiceDisplayName(application.service_type),
        daysSinceLastUpdate: application.daysSinceLastUpdate,
        missingDocuments,
      };

      // Send email using template integration service
      await this.emailService.sendMissingDocumentReminderEmail(
        recipientEmail,
        emailData,
      );

      this.reminderLogger.info(
        `Email sent to ${recipientEmail} for application ${application.application_number}`,
        'EmailSender',
      );
    } catch (error) {
      this.reminderLogger.error(
        `Failed to send email for application ${application.application_number}: ${error.message}`,
        'EmailSender',
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get display name for service type
   */
  private getServiceDisplayName(serviceType: string): string {
    const serviceNames: Record<string, string> = {
      immigration: 'Immigration Service',
      visa: 'Visa Application Service',
      consultation: 'Consultation Service',
      training: 'Training Service',
      package: 'Service Package',
    };

    return serviceNames[serviceType] || serviceType;
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): {
  mode: 'once' | 'scheduler' | 'status';
  help: boolean;
} {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    return { mode: 'once', help: true };
  }

  if (args.includes('--scheduler') || args.includes('-s')) {
    return { mode: 'scheduler', help: false };
  }

  if (args.includes('--status')) {
    return { mode: 'status', help: false };
  }

  return { mode: 'once', help: false };
}

/**
 * Display help information
 */
function displayHelp(): void {
  console.log(`
Missing Document Reminder System

Usage:
  npm run script:missing-document-reminder [options]
  ts-node scripts/missing-document-reminder.ts [options]

Options:
  --scheduler, -s    Run as a long-running scheduler process
  --status          Show scheduler status (if running)
  --help, -h        Show this help message

Environment Variables:
  SCHEDULER_ENABLED=true/false          Enable/disable scheduler
  SCHEDULER_HOUR=9                      Hour to run (0-23)
  SCHEDULER_MINUTE=0                    Minute to run (0-59)
  SCHEDULER_TIMEZONE=UTC                Timezone for scheduling
  SCHEDULER_CHECK_INTERVAL=60000        Check interval in milliseconds
  SCHEDULER_LOG_LEVEL=info              Log level (debug/info/warn/error)

Examples:
  # Run once immediately
  npm run script:missing-document-reminder

  # Run as scheduler (long-running process)
  npm run script:missing-document-reminder -- --scheduler

  # Run with custom schedule (daily at 2:30 PM)
  SCHEDULER_HOUR=14 SCHEDULER_MINUTE=30 npm run script:missing-document-reminder -- --scheduler
`);
}

/**
 * Execute one-time reminder process
 */
async function executeOnce(): Promise<void> {
  let app;

  try {
    // Create NestJS application context
    app = await NestFactory.createApplicationContext(
      MissingDocumentReminderModule,
      {
        logger: ['error', 'warn', 'log'],
      },
    );

    // Get services from DI container
    const reminderService = app.get(MissingDocumentReminderService);
    const emailService = app.get(EmailTemplateIntegrationService);
    const loggerService = app.get(LoggerService);

    // Create and execute script
    const script = new MissingDocumentReminderScript(
      reminderService,
      emailService,
      loggerService,
    );
    const result = await script.execute();

    // Output results
    console.log('\n=== Missing Document Reminder Results ===');
    console.log(`Success: ${result.success}`);
    console.log(`Applications Checked: ${result.totalApplicationsChecked}`);
    console.log(`Reminders Eligible: ${result.remindersEligible}`);
    console.log(`Reminders Sent: ${result.remindersSent}`);
    console.log(`Execution Time: ${result.executionTime}ms`);

    if (result.errors.length > 0) {
      console.log(`Errors: ${result.errors.length}`);
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    process.exit(result.success ? 0 : 1);
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

/**
 * Run scheduler mode
 */
async function runScheduler(): Promise<void> {
  try {
    console.log('Starting Missing Document Reminder Scheduler...');

    const scheduler = new MissingDocumentScheduler();
    await scheduler.start();

    // Keep the process running
    console.log('Scheduler is running. Press Ctrl+C to stop.');

    // The scheduler handles its own lifecycle and graceful shutdown
    // The process will exit when SIGTERM/SIGINT is received
  } catch (error) {
    console.error('Scheduler failed to start:', error);
    process.exit(1);
  }
}

/**
 * Show scheduler status
 */
async function showStatus(): Promise<void> {
  try {
    // This is a placeholder for status checking
    // In a production environment, you might want to implement
    // a status endpoint or shared state mechanism
    console.log('Status checking is not implemented in this version.');
    console.log('To check if scheduler is running, look for the process:');
    console.log('  ps aux | grep missing-document-reminder');
    console.log('  or check the log files in logs/reminders/');
  } catch (error) {
    console.error('Failed to get status:', error);
    process.exit(1);
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const { mode, help } = parseArguments();

  if (help) {
    displayHelp();
    return;
  }

  switch (mode) {
    case 'once':
      await executeOnce();
      break;
    case 'scheduler':
      await runScheduler();
      break;
    case 'status':
      await showStatus();
      break;
    default:
      console.error('Invalid mode');
      process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
