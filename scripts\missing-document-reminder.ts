#!/usr/bin/env ts-node

/**
 * Missing Document Remind<PERSON>
 *
 * This script implements a missing document reminder system that:
 * 1. Queries applications with missing documents
 * 2. Calculates days since last update
 * 3. Sends email reminders based on notification settings
 * 4. Logs all activities for debugging
 *
 * Can be run as a scheduled job or manually executed.
 *
 * Usage:
 *   npm run script:missing-document-reminder
 *   or
 *   ts-node scripts/missing-document-reminder.ts
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { PrismaService } from '../src/utils/prisma.service';
import { LoggerService } from '../src/utils/logger.service';
import { MailerService } from '../src/mailer/mailer.service';
import { NotificationSettingsStorageService } from '../src/utils/notification-settings-storage.service';
import { EmailTemplateIntegrationService } from '../src/application/services/email-template-integration.service';
import {
  MissingDocumentReminderService,
  MissingDocumentApplication,
} from './missing-document-reminder.service';
import { ReminderLogger } from './utils/reminder-logger';

// Create a minimal NestJS module for dependency injection
import { Module } from '@nestjs/common';

@Module({
  providers: [
    PrismaService,
    LoggerService,
    MailerService,
    NotificationSettingsStorageService,
    EmailTemplateIntegrationService,
    MissingDocumentReminderService,
  ],
})
class MissingDocumentReminderModule {}

interface ScriptResult {
  success: boolean;
  totalApplicationsChecked: number;
  remindersEligible: number;
  remindersSent: number;
  errors: string[];
  executionTime: number;
}

class MissingDocumentReminderScript {
  private readonly logger = new Logger(MissingDocumentReminderScript.name);
  private readonly reminderLogger = new ReminderLogger();

  constructor(
    private readonly reminderService: MissingDocumentReminderService,
    private readonly emailService: EmailTemplateIntegrationService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Main execution method
   */
  async execute(): Promise<ScriptResult> {
    const startTime = Date.now();
    const result: ScriptResult = {
      success: false,
      totalApplicationsChecked: 0,
      remindersEligible: 0,
      remindersSent: 0,
      errors: [],
      executionTime: 0,
    };

    try {
      this.logger.log('Starting missing document reminder process...');
      this.reminderLogger.info(
        'Missing document reminder process started',
        'ScriptExecution',
      );

      // Find applications with missing documents
      const queryStart = Date.now();
      const applications =
        await this.reminderService.findApplicationsWithMissingDocuments();
      const queryTime = Date.now() - queryStart;

      result.totalApplicationsChecked = applications.length;
      result.remindersEligible = applications.length;

      this.logger.log(
        `Found ${applications.length} applications eligible for reminders`,
      );
      this.reminderLogger.info(
        `Found ${applications.length} applications eligible for reminders`,
        'ScriptExecution',
      );
      this.reminderLogger.logDatabaseQuery(
        'findApplicationsWithMissingDocuments',
        queryTime,
        applications.length,
      );

      // Send reminders for each application
      for (const application of applications) {
        try {
          await this.sendReminderEmail(application);
          result.remindersSent++;

          this.logger.log(
            `Reminder sent for application: ${application.application_number}`,
          );
          this.logToFile(
            'INFO',
            `Reminder sent for application: ${application.application_number}`,
          );
        } catch (error) {
          const errorMsg = `Failed to send reminder for ${application.application_number}: ${error.message}`;
          result.errors.push(errorMsg);

          this.logger.error(errorMsg);
          this.logToFile('ERROR', errorMsg, error.stack);
        }
      }

      result.success = result.errors.length === 0;
      result.executionTime = Date.now() - startTime;

      // Log summary
      const summary = `Process completed. Sent ${result.remindersSent}/${result.remindersEligible} reminders in ${result.executionTime}ms`;
      this.logger.log(summary);
      this.logToFile('INFO', summary);

      if (result.errors.length > 0) {
        this.logger.warn(
          `${result.errors.length} errors occurred during execution`,
        );
        this.logToFile(
          'WARN',
          `${result.errors.length} errors occurred during execution`,
        );
      }

      return result;
    } catch (error) {
      result.success = false;
      result.executionTime = Date.now() - startTime;
      result.errors.push(`Script execution failed: ${error.message}`);

      this.logger.error(
        `Script execution failed: ${error.message}`,
        error.stack,
      );
      this.logToFile(
        'ERROR',
        `Script execution failed: ${error.message}`,
        error.stack,
      );

      throw error;
    }
  }

  /**
   * Send reminder email for a specific application
   */
  private async sendReminderEmail(
    application: MissingDocumentApplication,
  ): Promise<void> {
    try {
      const recipientEmail =
        this.reminderService.getRecipientEmail(application);
      const recipientName = this.reminderService.getRecipientName(application);

      // Transform missing documents for email template
      const missingDocuments = application.missingDocuments.map((doc) => ({
        fileName: doc.file_name,
        required: doc.required,
        status: doc.status,
        requestReason: doc.request_reason,
      }));

      // Prepare email data
      const emailData = {
        recipientName,
        applicationNumber: application.application_number,
        serviceName: this.getServiceDisplayName(application.service_type),
        daysSinceLastUpdate: application.daysSinceLastUpdate,
        missingDocuments,
      };

      // Send email using template integration service
      await this.emailService.sendMissingDocumentReminderEmail(
        recipientEmail,
        emailData,
      );

      this.logToFile(
        'INFO',
        `Email sent to ${recipientEmail} for application ${application.application_number}`,
      );
    } catch (error) {
      this.logToFile(
        'ERROR',
        `Failed to send email for application ${application.application_number}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get display name for service type
   */
  private getServiceDisplayName(serviceType: string): string {
    const serviceNames: Record<string, string> = {
      immigration: 'Immigration Service',
      visa: 'Visa Application Service',
      consultation: 'Consultation Service',
      training: 'Training Service',
      package: 'Service Package',
    };

    return serviceNames[serviceType] || serviceType;
  }

  /**
   * Ensure log directory exists
   */
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Log to file with timestamp
   */
  private logToFile(level: string, message: string, stack?: string): void {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level}] ${message}${stack ? `\nStack: ${stack}` : ''}\n`;

    try {
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  let app;

  try {
    // Create NestJS application context
    app = await NestFactory.createApplicationContext(
      MissingDocumentReminderModule,
      {
        logger: ['error', 'warn', 'log'],
      },
    );

    // Get services from DI container
    const reminderService = app.get(MissingDocumentReminderService);
    const emailService = app.get(EmailTemplateIntegrationService);
    const loggerService = app.get(LoggerService);

    // Create and execute script
    const script = new MissingDocumentReminderScript(
      reminderService,
      emailService,
      loggerService,
    );
    const result = await script.execute();

    // Output results
    console.log('\n=== Missing Document Reminder Results ===');
    console.log(`Success: ${result.success}`);
    console.log(`Applications Checked: ${result.totalApplicationsChecked}`);
    console.log(`Reminders Eligible: ${result.remindersEligible}`);
    console.log(`Reminders Sent: ${result.remindersSent}`);
    console.log(`Execution Time: ${result.executionTime}ms`);

    if (result.errors.length > 0) {
      console.log(`Errors: ${result.errors.length}`);
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    process.exit(result.success ? 0 : 1);
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
