/**
 * Missing Document Reminder Script Tests
 * 
 * Unit tests for the main script execution logic, error handling,
 * and integration scenarios.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationStatus } from '@prisma/client';
import { MissingDocumentReminderService } from '../../scripts/missing-document-reminder.service';
import { EmailTemplateIntegrationService } from '../../src/application/services/email-template-integration.service';
import { LoggerService } from '../../src/utils/logger.service';

// Mock the script class since we can't easily test the actual script execution
class MockMissingDocumentReminderScript {
  constructor(
    private readonly reminderService: MissingDocumentReminderService,
    private readonly emailService: EmailTemplateIntegrationService,
    private readonly loggerService: LoggerService,
  ) {}

  async execute() {
    const startTime = Date.now();
    const result = {
      success: false,
      totalApplicationsChecked: 0,
      remindersEligible: 0,
      remindersSent: 0,
      errors: [] as string[],
      executionTime: 0,
    };

    try {
      // Find applications with missing documents
      const applications = await this.reminderService.findApplicationsWithMissingDocuments();
      result.totalApplicationsChecked = applications.length;
      result.remindersEligible = applications.length;

      // Send reminders for each application
      for (const application of applications) {
        try {
          await this.sendReminderEmail(application);
          result.remindersSent++;
        } catch (error) {
          const errorMsg = `Failed to send reminder for ${application.application_number}: ${error.message}`;
          result.errors.push(errorMsg);
        }
      }

      result.success = result.errors.length === 0;
      result.executionTime = Date.now() - startTime;
      return result;

    } catch (error) {
      result.success = false;
      result.executionTime = Date.now() - startTime;
      result.errors.push(`Script execution failed: ${error.message}`);
      throw error;
    }
  }

  private async sendReminderEmail(application: any): Promise<void> {
    const recipientEmail = this.reminderService.getRecipientEmail(application);
    const recipientName = this.reminderService.getRecipientName(application);

    const missingDocuments = application.missingDocuments.map((doc: any) => ({
      fileName: doc.file_name,
      required: doc.required,
      status: doc.status,
      requestReason: doc.request_reason,
    }));

    const emailData = {
      recipientName,
      applicationNumber: application.application_number,
      serviceName: this.getServiceDisplayName(application.service_type),
      daysSinceLastUpdate: application.daysSinceLastUpdate,
      missingDocuments,
    };

    await this.emailService.sendMissingDocumentReminderEmail(recipientEmail, emailData);
  }

  private getServiceDisplayName(serviceType: string): string {
    const serviceNames: Record<string, string> = {
      immigration: 'Immigration Service',
      visa: 'Visa Application Service',
      consultation: 'Consultation Service',
      training: 'Training Service',
      package: 'Service Package',
    };

    return serviceNames[serviceType] || serviceType;
  }
}

describe('MissingDocumentReminderScript', () => {
  let script: MockMissingDocumentReminderScript;
  let reminderService: jest.Mocked<MissingDocumentReminderService>;
  let emailService: jest.Mocked<EmailTemplateIntegrationService>;
  let loggerService: jest.Mocked<LoggerService>;

  const mockReminderService = {
    findApplicationsWithMissingDocuments: jest.fn(),
    getRecipientEmail: jest.fn(),
    getRecipientName: jest.fn(),
  };

  const mockEmailService = {
    sendMissingDocumentReminderEmail: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: MissingDocumentReminderService,
          useValue: mockReminderService,
        },
        {
          provide: EmailTemplateIntegrationService,
          useValue: mockEmailService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    reminderService = module.get(MissingDocumentReminderService);
    emailService = module.get(EmailTemplateIntegrationService);
    loggerService = module.get(LoggerService);

    script = new MockMissingDocumentReminderScript(
      reminderService,
      emailService,
      loggerService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    it('should execute successfully with no applications', async () => {
      // Arrange
      mockReminderService.findApplicationsWithMissingDocuments.mockResolvedValue([]);

      // Act
      const result = await script.execute();

      // Assert
      expect(result).toMatchObject({
        success: true,
        totalApplicationsChecked: 0,
        remindersEligible: 0,
        remindersSent: 0,
        errors: [],
      });
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('should execute successfully and send reminders', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'immigration',
          status: ApplicationStatus.Pending,
          daysSinceLastUpdate: 7,
          missingDocuments: [
            {
              file_name: 'Passport Copy',
              required: true,
              status: 'pending',
              request_reason: 'Required for identity verification',
            },
          ],
        },
        {
          id: 'app-2',
          application_number: 'APP-2024-002',
          service_type: 'visa',
          status: ApplicationStatus.Pending,
          daysSinceLastUpdate: 14,
          missingDocuments: [
            {
              file_name: 'Bank Statement',
              required: false,
              status: 'rejected',
              request_reason: 'Document quality insufficient',
            },
          ],
        },
      ];

      mockReminderService.findApplicationsWithMissingDocuments.mockResolvedValue(mockApplications);
      mockReminderService.getRecipientEmail
        .mockReturnValueOnce('<EMAIL>')
        .mockReturnValueOnce('<EMAIL>');
      mockReminderService.getRecipientName
        .mockReturnValueOnce('John Doe')
        .mockReturnValueOnce('Jane Smith');
      mockEmailService.sendMissingDocumentReminderEmail.mockResolvedValue(undefined);

      // Act
      const result = await script.execute();

      // Assert
      expect(result).toMatchObject({
        success: true,
        totalApplicationsChecked: 2,
        remindersEligible: 2,
        remindersSent: 2,
        errors: [],
      });

      expect(mockEmailService.sendMissingDocumentReminderEmail).toHaveBeenCalledTimes(2);
      
      // Check first email call
      expect(mockEmailService.sendMissingDocumentReminderEmail).toHaveBeenNthCalledWith(
        1,
        '<EMAIL>',
        {
          recipientName: 'John Doe',
          applicationNumber: 'APP-2024-001',
          serviceName: 'Immigration Service',
          daysSinceLastUpdate: 7,
          missingDocuments: [
            {
              fileName: 'Passport Copy',
              required: true,
              status: 'pending',
              requestReason: 'Required for identity verification',
            },
          ],
        },
      );

      // Check second email call
      expect(mockEmailService.sendMissingDocumentReminderEmail).toHaveBeenNthCalledWith(
        2,
        '<EMAIL>',
        {
          recipientName: 'Jane Smith',
          applicationNumber: 'APP-2024-002',
          serviceName: 'Visa Application Service',
          daysSinceLastUpdate: 14,
          missingDocuments: [
            {
              fileName: 'Bank Statement',
              required: false,
              status: 'rejected',
              requestReason: 'Document quality insufficient',
            },
          ],
        },
      );
    });

    it('should handle partial failures gracefully', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'immigration',
          status: ApplicationStatus.Pending,
          daysSinceLastUpdate: 7,
          missingDocuments: [
            {
              file_name: 'Passport Copy',
              required: true,
              status: 'pending',
              request_reason: 'Required for identity verification',
            },
          ],
        },
        {
          id: 'app-2',
          application_number: 'APP-2024-002',
          service_type: 'visa',
          status: ApplicationStatus.Pending,
          daysSinceLastUpdate: 14,
          missingDocuments: [
            {
              file_name: 'Bank Statement',
              required: false,
              status: 'rejected',
              request_reason: 'Document quality insufficient',
            },
          ],
        },
      ];

      mockReminderService.findApplicationsWithMissingDocuments.mockResolvedValue(mockApplications);
      mockReminderService.getRecipientEmail
        .mockReturnValueOnce('<EMAIL>')
        .mockReturnValueOnce('<EMAIL>');
      mockReminderService.getRecipientName
        .mockReturnValueOnce('John Doe')
        .mockReturnValueOnce('Jane Smith');
      
      // First email succeeds, second fails
      mockEmailService.sendMissingDocumentReminderEmail
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Email service unavailable'));

      // Act
      const result = await script.execute();

      // Assert
      expect(result).toMatchObject({
        success: false,
        totalApplicationsChecked: 2,
        remindersEligible: 2,
        remindersSent: 1,
        errors: ['Failed to send reminder for APP-2024-002: Email service unavailable'],
      });
    });

    it('should handle database service failures', async () => {
      // Arrange
      const dbError = new Error('Database connection failed');
      mockReminderService.findApplicationsWithMissingDocuments.mockRejectedValue(dbError);

      // Act & Assert
      await expect(script.execute()).rejects.toThrow('Database connection failed');
    });

    it('should map service types to display names correctly', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'consultation',
          status: ApplicationStatus.Pending,
          daysSinceLastUpdate: 7,
          missingDocuments: [
            {
              file_name: 'Document',
              required: true,
              status: 'pending',
            },
          ],
        },
        {
          id: 'app-2',
          application_number: 'APP-2024-002',
          service_type: 'unknown_service',
          status: ApplicationStatus.Pending,
          daysSinceLastUpdate: 7,
          missingDocuments: [
            {
              file_name: 'Document',
              required: true,
              status: 'pending',
            },
          ],
        },
      ];

      mockReminderService.findApplicationsWithMissingDocuments.mockResolvedValue(mockApplications);
      mockReminderService.getRecipientEmail
        .mockReturnValueOnce('<EMAIL>')
        .mockReturnValueOnce('<EMAIL>');
      mockReminderService.getRecipientName
        .mockReturnValueOnce('John Doe')
        .mockReturnValueOnce('Jane Smith');
      mockEmailService.sendMissingDocumentReminderEmail.mockResolvedValue(undefined);

      // Act
      await script.execute();

      // Assert
      expect(mockEmailService.sendMissingDocumentReminderEmail).toHaveBeenNthCalledWith(
        1,
        '<EMAIL>',
        expect.objectContaining({
          serviceName: 'Consultation Service',
        }),
      );

      expect(mockEmailService.sendMissingDocumentReminderEmail).toHaveBeenNthCalledWith(
        2,
        '<EMAIL>',
        expect.objectContaining({
          serviceName: 'unknown_service', // Should use original value for unknown types
        }),
      );
    });
  });
});
