/**
 * Missing Document Reminder Service
 * 
 * Service for querying applications with missing documents and calculating
 * reminder timing based on notification settings.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../src/utils/prisma.service';
import { LoggerService } from '../src/utils/logger.service';
import { NotificationSettingsStorageService } from '../src/utils/notification-settings-storage.service';
import { ApplicationStatus } from '@prisma/client';

export interface MissingDocumentApplication {
  id: string;
  application_number: string;
  service_type: string;
  status: ApplicationStatus;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  created_at: Date;
  updated_at: Date;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  missingDocuments: {
    id: string;
    file_name: string;
    required: boolean;
    status: string;
    stage_order: number;
    request_reason?: string;
  }[];
  daysSinceLastUpdate: number;
  reminderDays: number;
}

@Injectable()
export class MissingDocumentReminderService {
  private readonly logger = new Logger(MissingDocumentReminderService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly loggerService: LoggerService,
    private readonly notificationSettingsService: NotificationSettingsStorageService,
  ) {}

  /**
   * Find applications with missing documents that need reminders
   */
  async findApplicationsWithMissingDocuments(): Promise<MissingDocumentApplication[]> {
    try {
      this.logger.log('Querying applications with missing documents...');

      // Query applications that are not completed and have pending documents
      const applications = await this.prisma.application.findMany({
        where: {
          status: {
            not: ApplicationStatus.Completed,
          },
          documents: {
            some: {
              OR: [
                { status: 'pending' },
                { status: 'rejected' },
                { file_url: '' }, // Empty file URL indicates missing document
              ],
            },
          },
        },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          documents: {
            where: {
              OR: [
                { status: 'pending' },
                { status: 'rejected' },
                { file_url: '' },
              ],
            },
            orderBy: { stage_order: 'asc' },
          },
        },
        orderBy: { updated_at: 'desc' },
      });

      this.logger.log(`Found ${applications.length} applications with missing documents`);

      // Process each application to calculate reminder timing
      const processedApplications: MissingDocumentApplication[] = [];

      for (const app of applications) {
        try {
          const daysSinceLastUpdate = this.calculateDaysSinceLastUpdate(app.updated_at);
          const reminderDays = await this.getReminderDaysForUser(app.user_id);

          // Only include applications that match the reminder timing and are within 30 days
          if (daysSinceLastUpdate === reminderDays && daysSinceLastUpdate <= 30) {
            const missingDocuments = app.documents.map(doc => ({
              id: doc.id,
              file_name: doc.file_name,
              required: doc.required,
              status: doc.status,
              stage_order: doc.stage_order,
              request_reason: doc.request_reason,
            }));

            processedApplications.push({
              id: app.id,
              application_number: app.application_number,
              service_type: app.service_type,
              status: app.status,
              user_id: app.user_id,
              guest_name: app.guest_name,
              guest_email: app.guest_email,
              created_at: app.created_at,
              updated_at: app.updated_at,
              user: app.user,
              missingDocuments,
              daysSinceLastUpdate,
              reminderDays,
            });
          }
        } catch (error) {
          this.loggerService.error(
            `Error processing application ${app.id}: ${error.message}`,
            error.stack,
            'MissingDocumentReminderService',
          );
        }
      }

      this.logger.log(`${processedApplications.length} applications qualify for reminders`);
      return processedApplications;

    } catch (error) {
      this.loggerService.error(
        `Failed to find applications with missing documents: ${error.message}`,
        error.stack,
        'MissingDocumentReminderService',
      );
      throw error;
    }
  }

  /**
   * Calculate days since last update
   */
  private calculateDaysSinceLastUpdate(updatedAt: Date): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - updatedAt.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  /**
   * Get reminder days setting for a user
   */
  private async getReminderDaysForUser(userId?: string): Promise<number> {
    try {
      if (!userId) {
        // Default reminder days for guest users
        return 7;
      }

      const settings = await this.notificationSettingsService.getSettings(userId);
      return settings?.missing_document_reminder_days || 7;
    } catch (error) {
      this.loggerService.error(
        `Failed to get reminder days for user ${userId}: ${error.message}`,
        error.stack,
        'MissingDocumentReminderService',
      );
      // Return default value on error
      return 7;
    }
  }

  /**
   * Get recipient email for application
   */
  getRecipientEmail(application: MissingDocumentApplication): string {
    if (application.user?.email) {
      return application.user.email;
    }
    if (application.guest_email) {
      return application.guest_email;
    }
    throw new Error(`No email found for application ${application.application_number}`);
  }

  /**
   * Get recipient name for application
   */
  getRecipientName(application: MissingDocumentApplication): string {
    if (application.user?.name) {
      return application.user.name;
    }
    if (application.guest_name) {
      return application.guest_name;
    }
    return 'User';
  }
}
