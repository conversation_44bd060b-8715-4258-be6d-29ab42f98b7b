#!/usr/bin/env ts-node

/**
 * Scheduler Manager CLI
 * 
 * Command-line interface for managing the missing document reminder scheduler
 * with start/stop/restart/status capabilities.
 * 
 * Usage:
 *   npm run scheduler:start
 *   npm run scheduler:stop
 *   npm run scheduler:restart
 *   npm run scheduler:status
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { ProcessManager } from './utils/process-manager';
import { ReminderLogger } from './utils/reminder-logger';
import { loadSchedulerConfig, formatConfigForLogging } from './config/scheduler-config';

/**
 * CLI commands
 */
type Command = 'start' | 'stop' | 'restart' | 'status' | 'help';

/**
 * Parse command line arguments
 */
function parseCommand(): Command {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    return 'help';
  }
  
  const command = args[0].toLowerCase();
  
  if (['start', 'stop', 'restart', 'status'].includes(command)) {
    return command as Command;
  }
  
  return 'help';
}

/**
 * Display help information
 */
function displayHelp(): void {
  console.log(`
Missing Document Reminder Scheduler Manager

Usage:
  npm run scheduler:<command>
  ts-node scripts/scheduler-manager.ts <command>

Commands:
  start     Start the scheduler as a background process
  stop      Stop the running scheduler process
  restart   Restart the scheduler process
  status    Show current scheduler status
  help      Show this help message

Examples:
  npm run scheduler:start
  npm run scheduler:stop
  npm run scheduler:restart
  npm run scheduler:status

Environment Variables:
  SCHEDULER_ENABLED=true/false          Enable/disable scheduler
  SCHEDULER_HOUR=9                      Hour to run (0-23)
  SCHEDULER_MINUTE=0                    Minute to run (0-59)
  SCHEDULER_TIMEZONE=UTC                Timezone for scheduling
  SCHEDULER_CHECK_INTERVAL=60000        Check interval in milliseconds
  SCHEDULER_LOG_LEVEL=info              Log level (debug/info/warn/error)

Log Files:
  logs/reminders/scheduler.log          Process output log
  logs/reminders/scheduler.pid          Process ID file
  logs/reminders/missing-document-reminders.log    Application log
`);
}

/**
 * Format uptime for display
 */
function formatUptime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Start command
 */
async function startCommand(): Promise<void> {
  const logger = new ReminderLogger();
  const processManager = new ProcessManager();
  
  try {
    console.log('Starting Missing Document Reminder Scheduler...');
    
    // Check if already running
    if (await processManager.isRunning()) {
      console.log('❌ Scheduler is already running');
      const processInfo = await processManager.getProcessInfo();
      if (processInfo) {
        console.log(`   PID: ${processInfo.pid}`);
        console.log(`   Started: ${processInfo.startTime.toISOString()}`);
      }
      process.exit(1);
    }
    
    // Load and display configuration
    const config = loadSchedulerConfig();
    if (!config.enabled) {
      console.log('❌ Scheduler is disabled in configuration');
      console.log('   Set SCHEDULER_ENABLED=true to enable');
      process.exit(1);
    }
    
    console.log('Configuration:');
    console.log(`   Schedule: ${config.schedule.hour.toString().padStart(2, '0')}:${config.schedule.minute.toString().padStart(2, '0')} ${config.schedule.timezone}`);
    console.log(`   Check Interval: ${config.intervals.checkInterval}ms`);
    console.log(`   Log Level: ${config.logging.level}`);
    
    // Start the process
    const success = await processManager.start();
    
    if (success) {
      console.log('✅ Scheduler started successfully');
      
      // Wait a moment and check status
      await new Promise(resolve => setTimeout(resolve, 2000));
      const processInfo = await processManager.getProcessInfo();
      
      if (processInfo && processInfo.status === 'running') {
        console.log(`   PID: ${processInfo.pid}`);
        console.log(`   Status: ${processInfo.status}`);
        console.log('   Use "npm run scheduler:status" to check status');
        console.log('   Use "npm run scheduler:stop" to stop');
      } else {
        console.log('⚠️  Process started but may have exited immediately');
        console.log('   Check logs for details: logs/reminders/scheduler.log');
      }
    } else {
      console.log('❌ Failed to start scheduler');
      console.log('   Check logs for details: logs/reminders/');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error starting scheduler:', error.message);
    logger.error('CLI start command failed', 'SchedulerManager', error.stack);
    process.exit(1);
  }
}

/**
 * Stop command
 */
async function stopCommand(): Promise<void> {
  const logger = new ReminderLogger();
  const processManager = new ProcessManager();
  
  try {
    console.log('Stopping Missing Document Reminder Scheduler...');
    
    // Check if running
    if (!(await processManager.isRunning())) {
      console.log('❌ Scheduler is not running');
      process.exit(1);
    }
    
    const processInfo = await processManager.getProcessInfo();
    if (processInfo) {
      console.log(`   Stopping PID: ${processInfo.pid}`);
    }
    
    // Stop the process
    const success = await processManager.stop();
    
    if (success) {
      console.log('✅ Scheduler stopped successfully');
    } else {
      console.log('❌ Failed to stop scheduler');
      console.log('   Check logs for details: logs/reminders/');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error stopping scheduler:', error.message);
    logger.error('CLI stop command failed', 'SchedulerManager', error.stack);
    process.exit(1);
  }
}

/**
 * Restart command
 */
async function restartCommand(): Promise<void> {
  const logger = new ReminderLogger();
  const processManager = new ProcessManager();
  
  try {
    console.log('Restarting Missing Document Reminder Scheduler...');
    
    // Restart the process
    const success = await processManager.restart();
    
    if (success) {
      console.log('✅ Scheduler restarted successfully');
      
      // Wait a moment and check status
      await new Promise(resolve => setTimeout(resolve, 2000));
      const processInfo = await processManager.getProcessInfo();
      
      if (processInfo && processInfo.status === 'running') {
        console.log(`   PID: ${processInfo.pid}`);
        console.log(`   Status: ${processInfo.status}`);
      }
    } else {
      console.log('❌ Failed to restart scheduler');
      console.log('   Check logs for details: logs/reminders/');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error restarting scheduler:', error.message);
    logger.error('CLI restart command failed', 'SchedulerManager', error.stack);
    process.exit(1);
  }
}

/**
 * Status command
 */
async function statusCommand(): Promise<void> {
  const logger = new ReminderLogger();
  const processManager = new ProcessManager();
  
  try {
    console.log('Missing Document Reminder Scheduler Status\n');
    
    const processInfo = await processManager.getProcessInfo();
    const config = loadSchedulerConfig();
    
    // Process status
    if (processInfo && processInfo.status === 'running') {
      console.log('✅ Status: Running');
      console.log(`   PID: ${processInfo.pid}`);
      console.log(`   Started: ${processInfo.startTime.toISOString()}`);
      
      const uptime = Date.now() - processInfo.startTime.getTime();
      console.log(`   Uptime: ${formatUptime(uptime)}`);
    } else {
      console.log('❌ Status: Not Running');
    }
    
    // Configuration
    console.log('\nConfiguration:');
    console.log(`   Enabled: ${config.enabled}`);
    console.log(`   Schedule: ${config.schedule.hour.toString().padStart(2, '0')}:${config.schedule.minute.toString().padStart(2, '0')} ${config.schedule.timezone}`);
    console.log(`   Check Interval: ${config.intervals.checkInterval}ms`);
    console.log(`   Retry Interval: ${config.intervals.retryInterval}ms`);
    console.log(`   Max Retries: ${config.intervals.maxRetries}`);
    console.log(`   Log Level: ${config.logging.level}`);
    
    // Next execution time
    if (config.enabled) {
      const now = new Date();
      const next = new Date();
      next.setHours(config.schedule.hour, config.schedule.minute, 0, 0);
      
      if (next <= now) {
        next.setDate(next.getDate() + 1);
      }
      
      console.log(`   Next Execution: ${next.toISOString()}`);
    }
    
    // Log files
    console.log('\nLog Files:');
    console.log('   Process Log: logs/reminders/scheduler.log');
    console.log('   Application Log: logs/reminders/missing-document-reminders.log');
    console.log('   Error Log: logs/reminders/missing-document-reminders-errors.log');
    console.log('   PID File: logs/reminders/scheduler.pid');
    
  } catch (error) {
    console.error('❌ Error getting status:', error.message);
    logger.error('CLI status command failed', 'SchedulerManager', error.stack);
    process.exit(1);
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const command = parseCommand();
  
  switch (command) {
    case 'start':
      await startCommand();
      break;
    case 'stop':
      await stopCommand();
      break;
    case 'restart':
      await restartCommand();
      break;
    case 'status':
      await statusCommand();
      break;
    case 'help':
    default:
      displayHelp();
      break;
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
