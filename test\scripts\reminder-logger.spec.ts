/**
 * Reminder Logger Utility Tests
 * 
 * Unit tests for the file-based logging utility used by the
 * missing document reminder system.
 * 
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import * as fs from 'fs';
import * as path from 'path';
import { ReminderLogger, LogLevel } from '../../scripts/utils/reminder-logger';

// Mock fs module
jest.mock('fs');

describe('ReminderLogger', () => {
  let logger: ReminderLogger;
  let mockFs: jest.Mocked<typeof fs>;
  const testLogDir = '/test/logs/reminders';

  beforeEach(() => {
    mockFs = fs as jest.Mocked<typeof fs>;
    mockFs.existsSync.mockReturnValue(true);
    mockFs.mkdirSync.mockReturnValue(undefined);
    mockFs.appendFileSync.mockReturnValue(undefined);
    mockFs.statSync.mockReturnValue({ size: 1000 } as any);
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'debug').mockImplementation();

    logger = new ReminderLogger(testLogDir);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should create log directory if it does not exist', () => {
      // Arrange
      mockFs.existsSync.mockReturnValue(false);

      // Act
      new ReminderLogger(testLogDir);

      // Assert
      expect(mockFs.mkdirSync).toHaveBeenCalledWith(testLogDir, { recursive: true });
    });

    it('should not create log directory if it already exists', () => {
      // Arrange
      mockFs.existsSync.mockReturnValue(true);

      // Act
      new ReminderLogger(testLogDir);

      // Assert
      expect(mockFs.mkdirSync).not.toHaveBeenCalled();
    });

    it('should use default log directory if none provided', () => {
      // Act
      const defaultLogger = new ReminderLogger();

      // Assert
      const expectedDir = path.join(process.cwd(), 'logs', 'reminders');
      expect(mockFs.existsSync).toHaveBeenCalledWith(expectedDir);
    });
  });

  describe('logging methods', () => {
    it('should log debug messages', () => {
      // Act
      logger.debug('Debug message', 'TestContext', { key: 'value' });

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders.log'),
        expect.stringContaining('[DEBUG] [TestContext] Debug message')
      );
      expect(console.debug).toHaveBeenCalledWith('[DEBUG] [TestContext] Debug message');
    });

    it('should log info messages', () => {
      // Act
      logger.info('Info message', 'TestContext', { key: 'value' });

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders.log'),
        expect.stringContaining('[INFO] [TestContext] Info message')
      );
      expect(console.log).toHaveBeenCalledWith('[INFO] [TestContext] Info message');
    });

    it('should log warning messages', () => {
      // Act
      logger.warn('Warning message', 'TestContext', { key: 'value' });

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders.log'),
        expect.stringContaining('[WARN] [TestContext] Warning message')
      );
      expect(console.warn).toHaveBeenCalledWith('[WARN] [TestContext] Warning message');
    });

    it('should log error messages to both files', () => {
      // Act
      logger.error('Error message', 'TestContext', 'Stack trace', { key: 'value' });

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders.log'),
        expect.stringContaining('[ERROR] [TestContext] Error message')
      );
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders-errors.log'),
        expect.stringContaining('[ERROR] [TestContext] Error message')
      );
      expect(console.error).toHaveBeenCalledWith('[ERROR] [TestContext] Error message');
    });
  });

  describe('specialized logging methods', () => {
    it('should log application processing start', () => {
      // Act
      logger.logApplicationProcessingStart('APP-001', '<EMAIL>');

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Processing application APP-001')
      );
    });

    it('should log application processing success', () => {
      // Act
      logger.logApplicationProcessingSuccess('APP-001', '<EMAIL>', 2);

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Successfully processed application APP-001')
      );
    });

    it('should log application processing error', () => {
      // Arrange
      const error = new Error('Processing failed');

      // Act
      logger.logApplicationProcessingError('APP-001', '<EMAIL>', error);

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Failed to process application APP-001')
      );
      // Should also write to error log
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders-errors.log'),
        expect.any(String)
      );
    });

    it('should log email sending attempts', () => {
      // Act
      logger.logEmailSendingAttempt('<EMAIL>', 'APP-001', 3);

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Attempting to send reminder email')
      );
    });

    it('should log email sending success', () => {
      // Act
      logger.logEmailSendingSuccess('<EMAIL>', 'APP-001');

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Successfully sent reminder email')
      );
    });

    it('should log email sending errors', () => {
      // Arrange
      const error = new Error('Email failed');

      // Act
      logger.logEmailSendingError('<EMAIL>', 'APP-001', error);

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Failed to send reminder email')
      );
    });

    it('should log database queries', () => {
      // Act
      logger.logDatabaseQuery('SELECT * FROM applications', 150, 5);

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Database query executed')
      );
    });

    it('should log execution summary', () => {
      // Act
      logger.logExecutionSummary({
        totalApplications: 10,
        eligibleApplications: 5,
        remindersSent: 3,
        errors: 2,
        executionTime: 1500,
      });

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Script execution completed')
      );
    });
  });

  describe('log formatting', () => {
    it('should include timestamp in log entries', () => {
      // Arrange
      const mockDate = new Date('2024-01-15T10:30:00.000Z');
      jest.spyOn(Date.prototype, 'toISOString').mockReturnValue(mockDate.toISOString());

      // Act
      logger.info('Test message');

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('[2024-01-15T10:30:00.000Z]')
      );
    });

    it('should include metadata in log entries', () => {
      // Act
      logger.info('Test message', 'TestContext', { applicationId: 'APP-001', userId: 'user-123' });

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('{"applicationId":"APP-001","userId":"user-123"}')
      );
    });

    it('should include stack trace for errors', () => {
      // Act
      logger.error('Error message', 'TestContext', 'Stack trace here');

      // Assert
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        expect.any(String),
        expect.stringContaining('Stack Trace:\nStack trace here')
      );
    });
  });

  describe('error handling', () => {
    it('should handle file write errors gracefully', () => {
      // Arrange
      const writeError = new Error('Disk full');
      mockFs.appendFileSync.mockImplementation(() => {
        throw writeError;
      });
      jest.spyOn(console, 'error').mockImplementation();

      // Act
      logger.info('Test message');

      // Assert
      expect(console.error).toHaveBeenCalledWith('Failed to write to log file', expect.any(String), writeError);
    });
  });

  describe('utility methods', () => {
    it('should return log file paths', () => {
      // Act
      const paths = logger.getLogFilePaths();

      // Assert
      expect(paths).toEqual({
        logFile: path.join(testLogDir, 'missing-document-reminders.log'),
        errorLogFile: path.join(testLogDir, 'missing-document-reminders-errors.log'),
      });
    });

    it('should clear log files', () => {
      // Arrange
      mockFs.existsSync.mockReturnValue(true);
      mockFs.unlinkSync.mockReturnValue(undefined);

      // Act
      logger.clearLogs();

      // Assert
      expect(mockFs.unlinkSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders.log')
      );
      expect(mockFs.unlinkSync).toHaveBeenCalledWith(
        path.join(testLogDir, 'missing-document-reminders-errors.log')
      );
    });

    it('should handle clear logs errors gracefully', () => {
      // Arrange
      const unlinkError = new Error('File not found');
      mockFs.unlinkSync.mockImplementation(() => {
        throw unlinkError;
      });
      jest.spyOn(console, 'error').mockImplementation();

      // Act
      logger.clearLogs();

      // Assert
      expect(console.error).toHaveBeenCalledWith('Failed to clear log files:', unlinkError);
    });
  });
});
